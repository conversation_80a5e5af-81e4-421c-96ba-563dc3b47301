import React, { useState, useEffect } from "react";
import { apiGet } from "./utils/api";

export default function UsageDashboard() {
  const [summary, setSummary] = useState(null);
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [logsLoading, setLogsLoading] = useState(false);
  const [pagination, setPagination] = useState({});
  const [filters, setFilters] = useState({
    model: '',
    date_from: '',
    date_to: '',
    page: 1,
    page_size: 20
  });

  // Load summary data
  const loadSummary = async () => {
    try {
      const response = await apiGet('/api/usage/summary/');
      setSummary(response);
    } catch (error) {
      console.error('Error loading usage summary:', error);
    }
  };

  // Load logs data
  const loadLogs = async (newFilters = filters) => {
    setLogsLoading(true);
    try {
      const params = new URLSearchParams();
      Object.entries(newFilters).forEach(([key, value]) => {
        if (value) params.append(key, value);
      });
      
      const response = await apiGet(`/api/usage/logs/?${params.toString()}`);
      setLogs(response.logs);
      setPagination(response.pagination);
    } catch (error) {
      console.error('Error loading usage logs:', error);
    } finally {
      setLogsLoading(false);
    }
  };

  // Export CSV
  const exportCSV = async () => {
    try {
      const params = new URLSearchParams();
      if (filters.model) params.append('model', filters.model);
      if (filters.date_from) params.append('date_from', filters.date_from);
      if (filters.date_to) params.append('date_to', filters.date_to);
      
      const token = localStorage.getItem("access");
      const response = await fetch(`/api/usage/export/?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `openai_usage_${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        alert('Error exporting CSV');
      }
    } catch (error) {
      console.error('Error exporting CSV:', error);
      alert('Error exporting CSV');
    }
  };

  // Handle filter changes
  const handleFilterChange = (key, value) => {
    const newFilters = { ...filters, [key]: value, page: 1 };
    setFilters(newFilters);
    loadLogs(newFilters);
  };

  // Handle pagination
  const handlePageChange = (newPage) => {
    const newFilters = { ...filters, page: newPage };
    setFilters(newFilters);
    loadLogs(newFilters);
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await Promise.all([loadSummary(), loadLogs()]);
      setLoading(false);
    };
    loadData();
  }, []);

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '50vh',
        fontSize: '18px',
        color: '#666'
      }}>
        Loading usage data...
      </div>
    );
  }

  return (
    <div style={{
      minHeight: "100vh",
      backgroundColor: "#f5f5f5",
      padding: "20px"
    }}>
      <div style={{
        maxWidth: "1200px",
        margin: "0 auto",
        backgroundColor: "white",
        borderRadius: "8px",
        boxShadow: "0 2px 10px rgba(0,0,0,0.1)",
        overflow: "hidden"
      }}>
        {/* Header */}
        <div style={{
          backgroundColor: "#2c3e50",
          color: "white",
          padding: "20px",
          textAlign: "center"
        }}>
          <h1 style={{ margin: 0, fontSize: "28px", fontWeight: "600" }}>
            OpenAI Usage Dashboard
          </h1>
          <p style={{ margin: "8px 0 0 0", opacity: 0.9 }}>
            Monitor API usage, costs, and performance metrics
          </p>
        </div>

        {/* Summary Cards */}
        {summary && (
          <div style={{
            display: "grid",
            gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
            gap: "20px",
            padding: "20px",
            backgroundColor: "#f8f9fa"
          }}>
            <SummaryCard
              title="Total Cost"
              value={`$${summary.total_cost.toFixed(4)}`}
              color="#e74c3c"
            />
            <SummaryCard
              title="Total Tokens"
              value={summary.total_tokens.toLocaleString()}
              color="#3498db"
            />
            <SummaryCard
              title="Total Queries"
              value={summary.total_queries.toLocaleString()}
              color="#2ecc71"
            />
            <SummaryCard
              title="Avg Cost/Query"
              value={`$${summary.avg_cost_per_query.toFixed(6)}`}
              color="#f39c12"
            />
          </div>
        )}

        {/* Models Usage */}
        {summary && summary.usage_by_model && Object.keys(summary.usage_by_model).length > 0 && (
          <div style={{ padding: "20px", borderBottom: "1px solid #eee" }}>
            <h3 style={{ marginBottom: "15px", color: "#2c3e50" }}>Usage by Model</h3>
            <div style={{
              display: "grid",
              gridTemplateColumns: "repeat(auto-fit, minmax(250px, 1fr))",
              gap: "15px"
            }}>
              {Object.entries(summary.usage_by_model).map(([model, data]) => (
                <div key={model} style={{
                  padding: "15px",
                  backgroundColor: "#f8f9fa",
                  borderRadius: "6px",
                  border: "1px solid #dee2e6"
                }}>
                  <h4 style={{ margin: "0 0 10px 0", color: "#495057" }}>{model}</h4>
                  <div style={{ fontSize: "14px", color: "#6c757d" }}>
                    <div>Queries: {data.count}</div>
                    <div>Cost: ${data.total_cost.toFixed(4)}</div>
                    <div>Tokens: {data.total_tokens.toLocaleString()}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Filters and Export */}
        <div style={{
          padding: "20px",
          backgroundColor: "#f8f9fa",
          borderBottom: "1px solid #eee",
          display: "flex",
          flexWrap: "wrap",
          gap: "15px",
          alignItems: "center"
        }}>
          <div>
            <label style={{ display: "block", marginBottom: "5px", fontWeight: "500" }}>
              Model:
            </label>
            <select
              value={filters.model}
              onChange={(e) => handleFilterChange('model', e.target.value)}
              style={{
                padding: "8px 12px",
                border: "1px solid #ddd",
                borderRadius: "4px",
                minWidth: "150px"
              }}
            >
              <option value="">All Models</option>
              {summary?.models_used?.map(model => (
                <option key={model} value={model}>{model}</option>
              ))}
            </select>
          </div>

          <div>
            <label style={{ display: "block", marginBottom: "5px", fontWeight: "500" }}>
              From Date:
            </label>
            <input
              type="date"
              value={filters.date_from}
              onChange={(e) => handleFilterChange('date_from', e.target.value)}
              style={{
                padding: "8px 12px",
                border: "1px solid #ddd",
                borderRadius: "4px"
              }}
            />
          </div>

          <div>
            <label style={{ display: "block", marginBottom: "5px", fontWeight: "500" }}>
              To Date:
            </label>
            <input
              type="date"
              value={filters.date_to}
              onChange={(e) => handleFilterChange('date_to', e.target.value)}
              style={{
                padding: "8px 12px",
                border: "1px solid #ddd",
                borderRadius: "4px"
              }}
            />
          </div>

          <div style={{ marginLeft: "auto" }}>
            <button
              onClick={exportCSV}
              style={{
                padding: "10px 20px",
                backgroundColor: "#28a745",
                color: "white",
                border: "none",
                borderRadius: "4px",
                cursor: "pointer",
                fontWeight: "500"
              }}
            >
              📊 Export CSV
            </button>
          </div>
        </div>

        {/* Logs Table */}
        <div style={{ padding: "20px" }}>
          <h3 style={{ marginBottom: "15px", color: "#2c3e50" }}>Usage Logs</h3>
          
          {logsLoading ? (
            <div style={{ textAlign: "center", padding: "40px", color: "#666" }}>
              Loading logs...
            </div>
          ) : (
            <>
              <div style={{ overflowX: "auto" }}>
                <table style={{
                  width: "100%",
                  borderCollapse: "collapse",
                  fontSize: "14px"
                }}>
                  <thead>
                    <tr style={{ backgroundColor: "#f8f9fa" }}>
                      <th style={tableHeaderStyle}>Timestamp</th>
                      <th style={tableHeaderStyle}>Model</th>
                      <th style={tableHeaderStyle}>Purpose</th>
                      <th style={tableHeaderStyle}>User</th>
                      <th style={tableHeaderStyle}>Tokens</th>
                      <th style={tableHeaderStyle}>Cost</th>
                      <th style={tableHeaderStyle}>Response Time</th>
                    </tr>
                  </thead>
                  <tbody>
                    {logs.map((log) => (
                      <tr key={log.id} style={{ borderBottom: "1px solid #eee" }}>
                        <td style={tableCellStyle}>
                          {new Date(log.request_timestamp).toLocaleString()}
                        </td>
                        <td style={tableCellStyle}>{log.model_name}</td>
                        <td style={tableCellStyle}>{log.purpose || '-'}</td>
                        <td style={tableCellStyle}>{log.user || '-'}</td>
                        <td style={tableCellStyle}>
                          {log.total_tokens.toLocaleString()}
                          <div style={{ fontSize: "12px", color: "#666" }}>
                            ({log.prompt_tokens}+{log.completion_tokens})
                          </div>
                        </td>
                        <td style={tableCellStyle}>${log.cost_usd.toFixed(6)}</td>
                        <td style={tableCellStyle}>
                          {log.response_time_ms ? `${log.response_time_ms}ms` : '-'}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              {pagination.total_pages > 1 && (
                <div style={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  gap: "10px",
                  marginTop: "20px"
                }}>
                  <button
                    onClick={() => handlePageChange(pagination.page - 1)}
                    disabled={!pagination.has_previous}
                    style={{
                      padding: "8px 16px",
                      border: "1px solid #ddd",
                      backgroundColor: pagination.has_previous ? "white" : "#f8f9fa",
                      cursor: pagination.has_previous ? "pointer" : "not-allowed",
                      borderRadius: "4px"
                    }}
                  >
                    Previous
                  </button>
                  
                  <span style={{ color: "#666" }}>
                    Page {pagination.page} of {pagination.total_pages}
                  </span>
                  
                  <button
                    onClick={() => handlePageChange(pagination.page + 1)}
                    disabled={!pagination.has_next}
                    style={{
                      padding: "8px 16px",
                      border: "1px solid #ddd",
                      backgroundColor: pagination.has_next ? "white" : "#f8f9fa",
                      cursor: pagination.has_next ? "pointer" : "not-allowed",
                      borderRadius: "4px"
                    }}
                  >
                    Next
                  </button>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
}

// Helper component for summary cards
function SummaryCard({ title, value, color }) {
  return (
    <div style={{
      padding: "20px",
      backgroundColor: "white",
      borderRadius: "6px",
      border: "1px solid #dee2e6",
      textAlign: "center",
      borderLeft: `4px solid ${color}`
    }}>
      <h4 style={{ margin: "0 0 10px 0", color: "#495057", fontSize: "14px" }}>
        {title}
      </h4>
      <div style={{ fontSize: "24px", fontWeight: "600", color: color }}>
        {value}
      </div>
    </div>
  );
}

// Table styles
const tableHeaderStyle = {
  padding: "12px 8px",
  textAlign: "left",
  fontWeight: "600",
  color: "#495057",
  borderBottom: "2px solid #dee2e6"
};

const tableCellStyle = {
  padding: "12px 8px",
  borderBottom: "1px solid #eee"
};
