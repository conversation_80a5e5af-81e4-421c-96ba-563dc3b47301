from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from rest_framework.exceptions import AuthenticationFailed
from django.contrib.auth import authenticate
from rest_framework import serializers

from .models import PromptTemplate, SupportTicket


# ─────────────────────────────────────────────
# 1. <PERSON><PERSON> (unchanged except for tiny cleanup)
# ─────────────────────────────────────────────
class EmailTokenObtainPairSerializer(TokenObtainPairSerializer):
    username_field = "official_email"

    def validate(self, attrs):
        email = attrs.get("official_email")
        password = attrs.get("password")

        if not email or not password:
            raise AuthenticationFailed("Email and password required")

        user = authenticate(
            request=self.context.get("request"),
            username=email,
            password=password,
        )
        if not user:
            raise AuthenticationFailed("Invalid credentials")

        refresh = self.get_token(user)

        return {
            "refresh": str(refresh),
            "access": str(refresh.access_token),
            "is_admin": user.is_staff,
            "user": {
                "id": user.id,
                "official_email": user.official_email,
                "name": user.name,
                "is_admin": user.is_staff,
            },
        }


# ─────────────────────────────────────────────
# 2. PromptTemplate
# ─────────────────────────────────────────────
class PromptTemplateSerializer(serializers.ModelSerializer):
    class Meta:
        model = PromptTemplate
        fields = ["id", "name", "template", "is_active", "last_modified"]
        read_only_fields = ["last_modified"]


# ─────────────────────────────────────────────
# 3. SupportTicket (normal API use)
# ─────────────────────────────────────────────
class SupportTicketSerializer(serializers.ModelSerializer):
    # 3a. Derivative / helper fields (read‑only by default)
    short_title = serializers.SerializerMethodField()
    problem_summary = serializers.SerializerMethodField()
    solution_summary = serializers.SerializerMethodField()

    class Meta:
        model = SupportTicket
        fields = "__all__"                     # includes product_type automatically
        read_only_fields = [
            "id",
            "user",
            "ticket_number",
            "created_at",
            "short_title",
            "problem_summary",
            "solution_summary",
        ]

    # 3b. Custom string helpers
    def get_short_title(self, obj):
        if obj.short_title:
            return obj.short_title
        if obj.problem_description:
            snippet = obj.problem_description.strip()[:50]
            return f"{snippet}…" if len(obj.problem_description) > 50 else snippet
        return "No title"

    def get_problem_summary(self, obj):
        if obj.problem_description:
            snippet = obj.problem_description.strip()[:100]
            return f"{snippet}…" if len(obj.problem_description) > 100 else snippet
        return "No problem description provided."

    def get_solution_summary(self, obj):
        # Show the full AI solution if present; otherwise fallback text
        return obj.solution_summary or "No solution yet."

    # 3c. Guard against invalid product_type values (optional but nice)
    def validate_product_type(self, value):
        valid_choices = [choice[0] for choice in SupportTicket.PRODUCT_TYPE_CHOICES]
        if value not in valid_choices:
            raise serializers.ValidationError(
                f"product_type must be one of {', '.join(valid_choices)}"
            )
        return value


# ─────────────────────────────────────────────
# 4. Escalated Tickets (admin views)
# ─────────────────────────────────────────────
class EscalatedTicketListSerializer(serializers.ModelSerializer):
    """
    Lightweight list view for admins—add product_type for quick triage if useful.
    """
    class Meta:
        model = SupportTicket
        fields = ("ticket_number", "created_at", "product_type")


class EscalatedTicketDetailSerializer(serializers.ModelSerializer):
    """
    Full detail view for admins—including the new product_type field.
    """
    user_full_name = serializers.CharField(source="user.name")
    user_email = serializers.EmailField(source="user.official_email")
    problem_summary = serializers.CharField(source="problem_summary")
    ai_solution = serializers.CharField(source="solution_summary")

    class Meta:
        model = SupportTicket
        fields = (
            "ticket_number",
            "user_full_name",
            "user_email",
            "product_type",       # ← NEW
            "purchased_from",
            "year_of_purchase",
            "product_name",
            "model",
            "serial_no",
            "operating_system",
            "problem_summary",
            "ai_solution",
            "status",
            "created_at",
        )
